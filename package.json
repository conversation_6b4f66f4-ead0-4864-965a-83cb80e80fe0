{"name": "a1d-canva-agent-experimental", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "run-p dev:*", "dev:web": "vite build --watch", "dev:server": "bun run --watch src/server/index.ts", "build": "vite build", "preview": "vite preview --port 8080"}, "dependencies": {"@canva/app-ui-kit": "^4.10.0", "@canva/asset": "^2.2.0", "@canva/design": "^2.6.0", "@canva/error": "^2.1.0", "@canva/platform": "^2.2.0", "@canva/user": "^2.1.0", "@hono/node-server": "^1.17.0", "hono": "^4.8.5", "react": "^19.1.0", "react-dom": "^19.1.0"}, "devDependencies": {"@eslint/js": "^9.30.1", "@types/bun": "^1.2.19", "@types/react": "^19.1.8", "@types/react-dom": "^19.1.6", "@vitejs/plugin-react": "^4.6.0", "eslint": "^9.30.1", "eslint-plugin-react-hooks": "^5.2.0", "eslint-plugin-react-refresh": "^0.4.20", "globals": "^16.3.0", "npm-run-all": "^4.1.5", "typescript": "~5.8.3", "typescript-eslint": "^8.35.1", "vite": "^7.0.4", "vite-plugin-css-injected-by-js": "^3.5.2", "vite-tsconfig-paths": "^5.1.4"}}