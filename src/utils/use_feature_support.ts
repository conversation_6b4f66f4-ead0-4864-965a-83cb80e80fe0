import { features } from "@canva/platform";
import type { Feature } from "@canva/platform";
import { useState, useEffect } from "react";

export function useFeatureSupport() {
  const [isSupported, setIsSupported] = useState(() => {
    return (...args: Feature[]) => features.isSupported(...args);
  });

  useEffect(() => {
    return features.registerOnSupportChange(() => {
      setIsSupported(() => {
        return (...args: Feature[]) => features.isSupported(...args);
      });
    });
  }, []);

  return isSupported;
}