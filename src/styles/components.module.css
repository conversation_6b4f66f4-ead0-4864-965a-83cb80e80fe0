.scrollContainer {
  box-sizing: border-box;
  overflow-y: scroll;
  height: 100%;
  padding-top: var(--ui-kit-space-2);
  padding-right: var(--ui-kit-space-2);
  padding-bottom: var(--ui-kit-space-2);

  scrollbar-width: thin;
  scrollbar-color: var(--ui-kit-color-typography-quaternary) transparent;
}

.scrollContainer::-webkit-scrollbar {
  position: absolute;
  width: var(--ui-kit-base-unit);
  height: 0;
}

.scrollContainer::-webkit-scrollbar-track {
  background: transparent;
  width: var(--ui-kit-base-unit);
  margin-top: var(--ui-kit-space-1);
  margin-bottom: var(--ui-kit-space-1);
}

.scrollContainer::-webkit-scrollbar-thumb {
  border-radius: var(--ui-kit-border-radius);
  background: var(--ui-kit-color-typography-quaternary);
  visibility: hidden;
}

.scrollContainer:hover::-webkit-scrollbar-thumb,
.scrollContainer:focus::-webkit-scrollbar-thumb,
.scrollContainer:focus-within::-webkit-scrollbar-thumb {
  visibility: visible;
}