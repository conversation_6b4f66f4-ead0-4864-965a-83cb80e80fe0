import { serve } from "@hono/node-server";
import { Hono } from "hono";
import { serveStatic } from "@hono/node-server/serve-static";

const app = new Hono();

// CORS 配置 - 允许 Canva 访问
app.use("*", async (c, next) => {
  // 设置 CORS 头部
  c.header("Access-Control-Allow-Origin", "*");
  c.header("Access-Control-Allow-Credentials", "true");
  c.header("Access-Control-Allow-Methods", "GET, POST, PUT, DELETE, OPTIONS");
  c.header("Access-Control-Allow-Headers", "Content-Type, Authorization, Access-Control-Allow-Private-Network");
  c.header("Access-Control-Allow-Private-Network", "true");

  // 处理 OPTIONS 预检请求
  if (c.req.method === "OPTIONS") {
    return c.text("", 200);
  }

  await next();
});

// 伺服静态文件
app.use("/*", serveStatic({
  root: "./dist",
  rewriteRequestPath: (path) => {
    // 将根路径重写为 app.js
    if (path === "/") {
      return "/app.js";
    }
    return path;
  },
}));



// 健康检查端点
app.get("/health", (c) => {
  return c.json({ status: "ok", timestamp: new Date().toISOString() });
});

// 404 处理
app.notFound((c) => {
  return c.text("Not Found", 404);
});

// 错误处理
app.onError((err, c) => {
  console.error("Server error:", err);
  return c.text("Internal Server Error", 500);
});

const port = parseInt(process.env.CANVA_FRONTEND_PORT || "8080");

console.log(`🚀 Hono server starting on port ${port}`);
console.log(`📦 Serving static files from: ./dist`);
console.log(`🌐 Access your app at: http://localhost:${port}/app.js`);

serve({
  fetch: app.fetch,
  port,
});
