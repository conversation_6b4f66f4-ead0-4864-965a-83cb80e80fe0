import { serve } from "@hono/node-server";
import { Hono } from "hono";
import { serveStatic } from "@hono/node-server/serve-static";

const app = new Hono();

// CORS 配置 - 允许 Canva 访问
app.use("*", async (c, next) => {
  // 设置 CORS 头部
  c.header("Access-Control-Allow-Origin", "*");
  c.header("Access-Control-Allow-Credentials", "true");
  c.header("Access-Control-Allow-Methods", "GET, POST, PUT, DELETE, OPTIONS");
  c.header("Access-Control-Allow-Headers", "Content-Type, Authorization, Access-Control-Allow-Private-Network");
  c.header("Access-Control-Allow-Private-Network", "true");

  // 处理 OPTIONS 预检请求
  if (c.req.method === "OPTIONS") {
    return c.text("", 200);
  }

  await next();
});

// 伺服构建好的 app.js 文件
app.get("/app.js", (c) => {
  const appJsPath = join(process.cwd(), "dist", "app.js");

  if (!existsSync(appJsPath)) {
    return c.text("app.js not found. Please run 'pnpm build' first.", 404);
  }

  try {
    const content = readFileSync(appJsPath, "utf-8");
    return c.text(content, 200, {
      "Content-Type": "application/javascript",
    });
  } catch (error) {
    console.error("Error reading app.js:", error);
    return c.text("Error reading app.js", 500);
  }
});

// 伺服 CSS 文件（如果存在）
app.get("/style.css", (c) => {
  const cssPath = join(process.cwd(), "dist", "style.css");

  if (!existsSync(cssPath)) {
    return c.text("/* No CSS file found */", 404);
  }

  try {
    const content = readFileSync(cssPath, "utf-8");
    return c.text(content, 200, {
      "Content-Type": "text/css",
    });
  } catch (error) {
    console.error("Error reading style.css:", error);
    return c.text("/* Error reading CSS */", 500);
  }
});

// 根路径重定向到 app.js（模拟 webpack-dev-server 的行为）
app.get("/", (c) => {
  return c.redirect("/app.js");
});

// 健康检查端点
app.get("/health", (c) => {
  return c.json({ status: "ok", timestamp: new Date().toISOString() });
});

// 404 处理
app.notFound((c) => {
  return c.text("Not Found", 404);
});

// 错误处理
app.onError((err, c) => {
  console.error("Server error:", err);
  return c.text("Internal Server Error", 500);
});

const port = parseInt(process.env.CANVA_FRONTEND_PORT || "8080");

console.log(`🚀 Hono server starting on port ${port}`);
console.log(`📦 Serving app.js from: ${join(process.cwd(), "dist", "app.js")}`);
console.log(`🌐 Access your app at: http://localhost:${port}/app.js`);

serve({
  fetch: app.fetch,
  port,
});
