# Canva 插件开发设置指南

## 快速开始

### 1. 安装依赖
```bash
npm install
```

### 2. 获取 Canva App ID
1. 访问 [Canva Developers Portal](https://www.canva.com/developers)
2. 创建一个新的应用或使用现有应用
3. 复制你的 App ID

### 3. 配置应用
编辑 `canva.config.json` 文件，将 `YOUR_APP_ID_HERE` 替换为你的实际 App ID：
```json
{
  "appId": "你的实际App ID",
  "appOrigin": "http://localhost:8080",
  "devServer": {
    "port": 8080,
    "protocol": "http"
  }
}
```

### 4. 开发模式

```bash
npm run dev
```
访问 http://localhost:8080 查看插件运行效果

### 5. 在 Canva 中测试插件
1. 登录 [Canva](https://www.canva.com)
2. 创建或打开一个设计
3. 在编辑器中点击"应用" → "开发者应用"
4. 输入你的开发服务器 URL: `http://localhost:8080`
5. 点击"预览"

### 6. 构建生产版本
```bash
npm run build
```
构建文件将输出到 `dist` 目录

## 功能说明

这个示例插件展示了：
- 拖放图片到 Canva 设计中
- 上传本地和外部图片资源
- 使用 Canva UI Kit 组件
- 响应式设计适配

## 项目结构
```
src/
├── App.tsx              # 主应用组件
├── main.tsx            # 应用入口
├── utils/              # 实用工具函数
│   ├── use_feature_support.ts
│   └── use_add_element.ts
├── styles/             # 样式文件
│   └── components.module.css
└── assets/             # 静态资源
    └── images/
        └── sample.svg
```

## 注意事项

1. **CORS 配置**: 开发服务器已配置允许跨域请求
2. **HTTPS**: 生产环境需要使用 HTTPS，但本地开发可以使用 HTTP
3. **外部资源**: 确保外部图片 URL 支持 CORS
4. **浏览器兼容**: 建议使用最新版 Chrome 或 Edge

## 常见问题

### Q: 插件无法加载？
A: 检查：
- App ID 是否正确配置
- 开发服务器是否运行在正确端口
- Canva 中输入的 URL 是否正确

### Q: 图片无法拖放？
A: 确保：
- 浏览器支持拖放 API
- 图片资源可访问且支持 CORS

### Q: 如何添加更多功能？
A: 参考 [Canva Apps SDK 文档](https://www.canva.dev/docs/apps/)