# CLAUDE.md

This file provides guidance to <PERSON> (claude.ai/code) when working with code in this repository.

## Project Overview

This is a Canva plugin built with React, TypeScript, and Vite. The project uses a specific architecture where:
- **Vite** is used ONLY for building the `app.js` bundle
- **Hono** serves the `/app.js` endpoint to deliver the built plugin to Canva

The app demonstrates drag-and-drop functionality for adding images to Canva designs and showcases best practices for Canva SDK integration with progressive enhancement and feature detection.

## Development Commands

```bash
bun run dev      # Start development server on port 8080
bun run build    # Build production bundle to dist/
bun run lint     # Run ESLint for code quality checks
bun run preview  # Preview production build locally
```

## Package Management

- **Package manager**: `pnpm` (NEVER use npm)
- **Script runner**: `bun`

## Architecture

### Core SDK Integration
The app integrates with Canva through these SDK packages:
- `@canva/app-ui-kit` - UI components and theming
- `@canva/asset` - Asset upload and management
- `@canva/design` - Design manipulation APIs
- `@canva/platform` - Platform features detection

### Key Patterns

1. **Feature Detection Pattern**: The codebase uses custom hooks (`useFeatureSupport`, `useAddElement`) to detect and adapt to available Canva features at runtime. Always check feature support before using SDK methods.

2. **Asset Upload Flow**: Images must be uploaded to Canva's CDN before adding to designs:
   ```typescript
   const { ref } = await upload({ mimeType, url, thumbnailUrl, type: "image", ... });
   addElementAtPoint({ type: "image", ref, altText });
   ```

3. **Progressive Enhancement**: The app gracefully falls back from `addElementAtPoint` to `addElementAtCursor` and from `startDragToPoint` to `startDragToCursor` based on feature availability.

### Project Structure
- `src/App.tsx` - Main component with drag-drop implementation
- `src/utils/` - Custom hooks for Canva SDK integration
- `src/main.tsx` - App entry point with AppUiProvider wrapper
- `vite.config.ts` - Build config with path aliases

## Development Workflow

1. **Testing in Canva**:
   - Start dev server: `bun run dev`
   - Open Canva editor
   - Navigate to Apps → Developer Apps
   - Enter URL: `http://localhost:8080`
   - Click Preview to test

2. **Before Committing**:
   - Run `bun run lint` to check code quality
   - Build with `bun run build` to verify production build

## Important Notes

- The app ID must be configured in `canva.config.json` (obtain from Canva Developers Portal)
- Always wrap the app root with `AppUiProvider` from `@canva/app-ui-kit`
- Use Canva's UI components for consistent styling
- Include proper metadata when uploading assets (dimensions, mimeType, aiDisclosure)
- Check feature support before using any SDK method that might not be universally available