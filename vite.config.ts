import { defineConfig } from "vite";
import react from "@vitejs/plugin-react";
import tsconfigPaths from "vite-tsconfig-paths";
import canvaConfig from "./canva.config.json";
import cssInjectedByJsPlugin from "vite-plugin-css-injected-by-js";

// https://vite.dev/config/
export default defineConfig({
	plugins: [react(), tsconfigPaths(), cssInjectedByJsPlugin()],
	server: {
		port: 8080,
		cors: {
			origin: "*",
			credentials: true,
		},
		headers: {
			"Access-Control-Allow-Private-Network": "true",
		},
	},
	define: {
		"import.meta.env.CANVA_APP_ID": JSON.stringify(canvaConfig.appId),
		"import.meta.env.CANVA_APP_ORIGIN": JSON.stringify(canvaConfig.appOrigin),
	},
	build: {
		rollupOptions: {
			input: {
				app: "./src/main.tsx",
			},
			output: {
				entryFileNames: "[name].js",
				chunkFileNames: "[name].js",
				assetFileNames: "[name].[ext]",
				// 强制所有代码打包到一个文件
				manualChunks: undefined,
				inlineDynamicImports: true,
			},
		},
		cssCodeSplit: false,
		sourcemap: false,
		emptyOutDir: true,
		// 禁用代码分割
		chunkSizeWarningLimit: 10000,
	},
});
